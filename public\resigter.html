<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SterilEase - Sign In / Sign Up</title>
    <link rel="stylesheet" href="styles.css">
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/9.15.0/firebase-app.js";
        import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, sendPasswordResetEmail } from "https://www.gstatic.com/firebasejs/9.15.0/firebase-auth.js";

        const firebaseConfig = {
            apiKey: "AIzaSyAnU69sqRHEC9xDj7sQkyBH_sOG3AOhF24",
            authDomain: "sterilease-b1185.firebaseapp.com",
            databaseURL: "https://sterilease-b1185-default-rtdb.asia-southeast1.firebasedatabase.app",
            projectId: "sterilease-b1185",
            storageBucket: "sterilease-b1185.appspot.com",
            messagingSenderId: "287619655835",
            appId: "1:287619655835:web:3a7bf5fa9372d8bef33e3e"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth();
        
        document.addEventListener("DOMContentLoaded", () => {
            const signInForm = document.getElementById("signInForm");
            const signUpForm = document.getElementById("signupForm");
            const forgotPasswordModal = document.getElementById("modal");

            // Sign In Handler
            signInForm.addEventListener("submit", async (e) => {
                e.preventDefault();
                const email = document.getElementById("email").value;
                const password = document.getElementById("password").value;

                try {
                    const userCredential = await signInWithEmailAndPassword(auth, email, password);
                    alert(`Welcome back, ${userCredential.user.email}!`);
                    window.location.href = "trial.html"; // Redirect to dashboard
                    const userId = userCredential.user.uid;
                } catch (error) {
                    alert(`Sign-in failed: ${error.message}`);
                }
            });

            // Sign Up Handler
            signUpForm.addEventListener("submit", async (e) => {
                e.preventDefault();
                const email = document.getElementById("emailSignup").value;
                const password = document.getElementById("passwordSignup").value;
                const confirmPassword = document.getElementById("confirmPasswordSignup").value;

                if (password !== confirmPassword) {
                    alert("Passwords do not match!");
                    return;
                }

                try {
                    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                    alert(`Account created successfully for ${userCredential.user.email}`);
                    toggleForm();
                } catch (error) {
                    alert(`Sign-up failed: ${error.message}`);
                }
            });

            // Forgot Password Handler
            document.getElementById("forgotPasswordLink").addEventListener("click", () => {
                forgotPasswordModal.style.display = "flex";
            });

            document.getElementById("closeModal").addEventListener("click", () => {
                forgotPasswordModal.style.display = "none";
            });

            document.getElementById("sendRecoveryLink").addEventListener("click", async () => {
                const email = document.getElementById("emailInput").value;
                try {
                    await sendPasswordResetEmail(auth, email);
                    alert(`Password recovery link sent to ${email}`);
                    forgotPasswordModal.style.display = "none";
                } catch (error) {
                    alert(`Error sending recovery link: ${error.message}`);
                }
            });
        });

        // Toggle Form View
        window.toggleForm = () => {
            document.querySelectorAll(".form").forEach((form) => form.classList.toggle("active"));
            document.getElementById("formTitle").innerText =
                document.getElementById("formTitle").innerText === "Sign In" ? "Sign Up" : "Sign In";
        };

    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to bottom, #0867d490, #ffffff);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .main-container {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 90%;
            gap: 20px;
        }

        .image-container {
            flex: 1;
        }

        .image-container img {
            width: 100%;
            border-radius: 8px;
            object-fit: cover;
        }

        .form-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .heading {
            font-size: 2rem;
            font-weight: bold;
            color: #004085;
            margin-bottom: 20px;
        }

        form {
            width: 100%;
            max-width: 300px;
            display: none;
            flex-direction: column;
        }

        form.active {
            display: flex;
        }

        label {
            text-align: left;
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: #333;
        }

        input {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        button {
            background: #007BFF;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s, background 0.3s;
        }

        button:hover {
            background: #0056b3;
            transform: scale(1.05);
        }

        .toggle-link {
            color: #007BFF;
            cursor: pointer;
            text-decoration: underline;
        }

        .toggle-link:hover {
            color: #0056b3;
        }
            /* Modal overlay background */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                display: none;
            }
    
            /* Modal content box */
            .modal-content {
                background-color: white;
                padding: 20px;
                width: 400px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                text-align: center;
                position: relative;
            }
    
            /* Modal header */
            .modal-content h3 {
                margin-top: 0;
                color: #052f5f;
                font-size: 22px;
            }
    
            /* Input box */
            .modal-content input {
                width: 100%;
                padding: 10px;
                margin: 15px 0;
                border: 1px solid #ccc;
                border-radius: 5px;
                font-size: 16px;
            }
    
            /* Submit button */
            .modal-content button {
                background-color: #052f5f;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 16px;
                cursor: pointer;
                transition: background-color 0.3s ease;
            }
    
            .modal-content button:hover {
                background-color: #03416a;
            }
    
            /* Close button */
            .modal-close {
                position: absolute;
                top: 15px;
                right: 20px;
                font-size: 20px;
                color: #666;
                cursor: pointer;
            }
    
            .modal-close:hover {
                color: #000;
            }

    </style>
</head>
<body>
    <div class="main-container">
        <div class="image-container">
            <img src="/assests/sterilease.jpeg" alt="SterilEase Logo" />
        </div>

        <div class="form-container">
            <h1 id="formTitle">Sign In</h1>

            <!-- Sign In Form -->
            <form id="signInForm" class="form active">
                <label for="email">Email:</label>
                <input type="email" id="email" placeholder="Enter your email" required>
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter your password" required>
                <button type="submit">Login</button>
                <p><a id="forgotPasswordLink" href="#">Forgot Password?</a></p>
                <p>Don't have an account? <span class="toggle-link" onclick="toggleForm()">Register</span></p>
            </form>

            <!-- Sign Up Form -->
            <form id="signupForm" class="form">
                <label for="emailSignup">Email:</label>
                <input type="email" id="emailSignup" placeholder="Enter your email" required>
                <label for="passwordSignup">Password:</label>
                <input type="password" id="passwordSignup" placeholder="Enter your password" required>
                <label for="confirmPasswordSignup">Confirm Password:</label>
                <input type="password" id="confirmPasswordSignup" placeholder="Re-enter your password" required>
                <button type="submit">Register</button>
                <p>Already have an account? <span class="toggle-link" onclick="toggleForm()">Login</span></p>
            </form>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div id="modal" class="modal-overlay">
        <div class="modal-content">
            <span id="closeModal" class="modal-close">&times;</span>
            <h3>Password Recovery</h3>
            <input type="email" id="emailInput" placeholder="Enter your email" required>
            <button id="sendRecoveryLink">Send Recovery Link</button>
        </div>
    </div>
</body>
</html>