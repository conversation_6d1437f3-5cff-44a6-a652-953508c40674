# SterilEase Sample Data Implementation

## Overview
This implementation adds realistic sample data to all charts in the SterilEase dashboard application. The sample data simulates real IoT sensor readings from a smart restroom system.

## Features Added

### 📊 Enhanced Charts with Sample Data

#### 1. **Power Consumption Chart**
- **Daily View**: 7 days of power consumption (2-8 kWh per day)
- **Monthly View**: 12 months of power consumption (60-240 kWh per month)
- **Yearly View**: 5 years of power consumption (720-2880 kWh per year)
- **Enhanced Styling**: Gradient fills, better tooltips, and smooth animations

#### 2. **Water Consumption Chart**
- **Daily View**: 7 days of water usage (50-200L per day)
- **Monthly View**: 12 months of water usage (1500-6000L per month)
- **Yearly View**: 5 years of water usage (18000-72000L per year)
- **Interactive Elements**: Hover effects and detailed tooltips

#### 3. **Flush Count Chart**
- **Daily View**: 7 days of flush counts (15-45 flushes per day)
- **Monthly View**: 12 months of flush counts (450-1350 flushes per month)
- **Yearly View**: 5 years of flush counts (5400-16200 flushes per year)
- **Bar Chart Design**: Rounded corners and gradient colors

#### 4. **Septic Tank Status Chart**
- **Pie Chart**: Shows available vs used capacity
- **Realistic Data**: 35% full, 65% available capacity
- **Dynamic Updates**: Simulates real-time capacity changes

### 🔄 Real-time Simulation
- **Auto-updates**: Charts update every 30 seconds with small variations
- **Realistic Changes**: Simulates actual sensor fluctuations
- **Smooth Animations**: Charts animate smoothly during updates

### 💰 Enhanced Savings Calculations
- **Water Savings**: Calculates savings based on SterilEase (2L) vs normal restrooms (8L) per flush
- **Electricity Savings**: Calculates savings based on SterilEase (0.2kWh) vs normal restrooms (0.5kWh) per flush
- **Dynamic Updates**: Savings update automatically with flush count changes

## Technical Implementation

### Sample Data Generation
```javascript
function generateSampleData() {
    // Generates realistic data for daily, monthly, and yearly views
    // Uses current date as reference point
    // Creates appropriate ranges for each metric
}
```

### Chart Enhancement
- **Improved Styling**: Better colors, gradients, and visual appeal
- **Enhanced Tooltips**: More informative hover information
- **Smooth Animations**: Professional chart transitions
- **Responsive Design**: Charts adapt to different screen sizes

### Time-based Filtering
- **Day View**: Shows past 7 days with daily granularity
- **Month View**: Shows past 12 months with monthly totals
- **Year View**: Shows past 5 years with yearly totals

## Firebase Data Structure

The application expects Firebase data in the following structure (see `public/sample-firebase-data.json`):

```json
{
  "FlushCount": {
    "dailyFlushes": [23, 31, 28, 35, 29, 33, 27]
  },
  "PowerConsumption": {
    "dailyUsage": [4.2, 5.8, 3.9, 6.1, 4.7, 5.3, 4.8]
  },
  "WaterConsumption": {
    "dailyConsumption": [92, 124, 78, 142, 106, 118, 95]
  },
  "SepticTankStatus": {
    "history": [
      {"timestamp": "2025-01-11T08:00:00Z", "value": 35}
    ]
  }
}
```

## Usage

### Running the Application
1. Start the Electron app: `npm start`
2. The dashboard will load with sample data automatically
3. Use the Day/Month/Year buttons to switch between time views
4. Charts will update in real-time every 30 seconds

### Button Functionality
- **Day Button**: Shows daily data for the past 7 days
- **Month Button**: Shows monthly data for the past 12 months
- **Year Button**: Shows yearly data for the past 5 years

### Real-time Updates
- Charts automatically update every 30 seconds
- Small random variations simulate real sensor readings
- Septic tank level changes dynamically
- Savings calculations update automatically

## Benefits

1. **Immediate Visual Feedback**: No need to wait for real IoT data
2. **Professional Appearance**: Charts look polished and production-ready
3. **Interactive Experience**: Users can explore different time ranges
4. **Realistic Simulation**: Data ranges match actual smart restroom usage
5. **Development Ready**: Easy to replace sample data with real Firebase data

## Future Integration

When real IoT sensors are connected:
1. Replace sample data generation with Firebase listeners
2. The chart update mechanisms are already in place
3. Data structure matches expected Firebase format
4. Real-time updates will work seamlessly

## Files Modified

- `public/main.js`: Enhanced with sample data generation and improved chart styling
- `public/sample-firebase-data.json`: Reference Firebase data structure
- `SAMPLE_DATA_README.md`: This documentation file

The implementation maintains compatibility with the existing Firebase integration while providing immediate visual feedback for development and demonstration purposes.
