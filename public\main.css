 
 
 /* General link styles */
a {
    color: inherit; /* Ensures the link inherits the text color */
    text-decoration: none; /* Removes the underline */
}

a:visited {
    color: inherit; /* Prevents color change for visited links */
}

a:hover {
    color: #030303; /* Optional: Change color on hover for a better UX */
}

a:active {
    color: hsl(0, 0%, 0%); /* Optional: Define color when the link is active */
}

@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');
*{
    margin: 0;
    padding: 0; 
    box-sizing: border-box; 
    font-family: sans-serif;
}
.slide{
    height: 100vh;
    width: 180px;
    position: fixed;
    top: 0 ;
    left: 0 ;
    background-color: #052f5f;
    transition: 0.6s ease;
    transform: translateX(-180px);
    z-index: 1000;
    border-radius: 0 90px 90px 0;
    padding-top: 4%;
    padding-left: 15px;
}

ul li{
    color: rgb(229, 222, 222);
    list-style: none;
}

ul li a{
    color: rgb(229, 222, 222);
    font-weight: 500;
    padding: 5px 0;
    display: block;
    text-transform: capitalize;
    text-decoration: none;
    transform: 0.2s ease-bout;
}


ul li a i{
    width: 40px;
    text-align: center;
}

input{
    display: none;
    visibility: hidden;
    -webkit-appearance: none;
    appearance: none;
}

body {        
    font-family: Arial, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

h2 {
    text-align: center;
    color: #052f5f;
    margin-bottom: 10px;
    font-size: 2.5rem;
    font-weight: bold;
    border: 4px solid #052f5f;
    padding: 5px;
    background-color: #f1f1f1;
    border-radius: 20px;
}

 /* Responsive Design */
 @media (max-width: 768px) {
     .chart-box1,
     .chart-box2 {
         padding: 10px;
     }
 }

 .modal {
    display: none;
    position: fixed;
    z-index: 1001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    overflow: auto;
}


h4 {
    margin-bottom: 10px;
    font-size: 18px; /* Adjust the size */
    font-weight: bold; /* Make it bold */
    color: #333; /* Dark gray for readability */
    text-align: center; /* Center the text */
    margin: 10px 0; /* Add space around */
    text-transform: uppercase; /* Make text uppercase */
    letter-spacing: 1px; /* Add spacing between letters // Optional: underline effect */
    padding-bottom: 5px; /* Space below text */
}

/* Pop up styles*/
.popup {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: auto;
}

.popup-content {
    position: relative;
    margin: 10% auto;
    padding: 20px 30px;
    width: 60%;
    max-width: 600px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.3);
    font-family: 'Arial', sans-serif;
    animation: fadeIn 0.5s ease-in-out;
    text-align: center;
}


.popup-title {
    font-size: 24px;
    color: #052f5f;
    font-weight: bold;
    margin-bottom: 10px;
}

.popup-description {
    font-size: 16px;
    color: #444;
    line-height: 1.6;
    margin-bottom: 20px;
}

.features-title {
    font-size: 18px;
    color: #007bff;
    margin-bottom: 15px;
    font-weight: bold;
}

.features-list {
    list-style: none;
    padding: 0;
    font-size: 16px;
    color: #000000;
    text-align: left;
}

.features-list li {
    margin-bottom: 10px;
    padding-left: 30px;
    position: relative;
}

.features-list li i {
    position: absolute;
    left: 0;
    top: 0;
    font-size: 20px;
    color: #007bff;
}

/* Style for the Menu */
.menu {
    background-color: #333;
    color: white;
    padding: 20px;
    text-align: center;
}
.menu ul {
    color: white;
    list-style: none;
    padding: 0;
}
.menu ul li {
    color: white;
    margin: 10px;
}

/* Style for the Popup */
.popup {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4); /* Black with opacity */
    padding-top: 60px;
}

/* Popup Content */
.popup-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
}
.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}
    
/* Modal Styles */

.modal-content {
    position: relative;
    margin: 10% auto;
    padding: 20px;
    width: 80%;
    max-width: 800px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.3);
    text-align: center;
    font-family: 'Arial', sans-serif;
    animation: fadeIn 0.5s ease-in-out;
}

.close {
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 40px;
    font-weight: bold;
    color: #555;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: #d9534f;
}

.modal-title {
    font-size: 24px;
    font-weight: bold;
    color: #052f5f;
    margin-bottom: 15px;
}

.modal-intro {
    font-size: 18px;
    color: #444;
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Modal Body with Image */
.modal-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 20px;
}

.modal-image {
    width: 200px;
    height: auto;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.modal-description p {
    font-size: 16px;
    color: #666;
    line-height: 1.8;
    text-align: justify;
}

/* Tools Section */
.tools-title {
    font-size: 20px;
    color: #052f5f;
    margin-bottom: 15px;
    font-weight: bold;
    text-align: center;
}
.tools-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
}

.tool {
    text-align: center;
    max-width: 150px;
}

.tool img {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
}

.tool p {
    font-size: 14px;
    color: #444;
}

#charts-section {
    display: flex;
    flex-direction: column; /* Arrange items in a column */
    align-items: center; /* Center align the charts */
    margin-top: 20px;
}

#charts-section {
    display: flex;
    flex-direction: column; /* Arrange items in a column */
    align-items: center; /* Center align the charts */
    margin-top: 20px;
}

#flushes-chart-section {
    margin-bottom: 20px; /* Add space between the charts */
}

.chart-container h3 {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 800;
    margin-bottom: 1px 0;
}

.chart-container {
    width: 100%;
    max-width: 700px;
    height: auto; 
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    margin: 0 auto; 
}
.predictive-box {
    margin-top: 20px;
    border-radius: 5px;
    position: relative;
    width: 100%;
    height: 30px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    overflow: hidden;
    margin: 10px 0;
  }
  
  .fill {
    transition: width 0.5s ease;
    height: 100%;
    background-color: green;
    border-radius: 5px;
    width: 0%;
  }
  
  .threshold-line {
    position: absolute;
    top: 0;
    left: 80%; /* Adjust this value to match the threshold */
    width: 2px;
    height: 100%;
    background-color: rgb(191, 20, 20);
  }
  
  .alert-message {
    display: none;
    margin-top: 10px;
    color: red;
    font-weight: bold;
  }
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
}

/* Chart Section */
.chart-section {
    position: relative;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #f9f9f9;
    max-width: 700px; /* Increased size of the container */
    margin: 0 auto;
}

/* Container for both Septic Tank and Predictive Maintenance */
.septic-maintenance-container {
    grid-column: span 1;
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

/* Predictive Maintenance and Septic Tank */
.predictive-box-container {
    padding: 10px;
    background-color: white;
    border-radius: 10px;
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.threshold-line {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background-color: transparent;
}
.alert-message {
    color: red;
    font-weight: bold;
    display: none;
    text-align: center;
    margin-top: 5px;
}

/* Button Group Styling */
.button-group button {
    background-color: #0a3d8a;
    color: white;
    border: none;
    padding: 10px;
    margin: 5px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s;
}
.button-group button:hover {
    background-color: rgb(8, 107, 183);
    box-shadow: 0 6px 8px 0 rgba(10, 74, 202, 0.676), 0 8px 20px 0 rgba(39, 94, 212, 0.4);
}

.button-group button:visited{
    background-color: #007bff;
}

.button-group button:active{
    background-color: #007bff;
    box-shadow: 2px 3px #1f84d7a2;
    transform: translateY(2px);
}
/* Larger Pie Chart */
#resourcePieChart {
    width: 300px !important; /* Increased size of pie chart */
    height: 300px !important;
    margin: 0 auto;
}
/* Flushes Chart */
.flushes-chart {
    padding: 20px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #f9f9f9;
    width: 100%;  /* Ensures full width */
    max-width: 700px; /* Adjusts the size */
    margin: 0 auto; /* Centers the chart */
}

.flushes-chart canvas {
    width: 100% !important; /* Make the canvas take full width */
    height: 400px !important; /* Make the height bigger */
}
/* Power Consumption Chart */
.power-chart {
    padding: 20px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #f9f9f9;
    width: 100%;  /* Ensures full width */
    max-width: 700px; /* Adjust the max width as desired */
    margin: 0 auto; /* Centers the chart */
}

.power-chart canvas {
    width: 100% !important; /* Makes the canvas take full width */
    height: 400px !important; /* Increases the height of the chart */
}

/* Water Consumption Chart */
.water-chart {
    padding: 20px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #f9f9f9;
    width: 100%;  /* Ensures full width */
    max-width: 700px; /* Adjust the max width as desired */
    margin: 0 auto; /* Centers the chart */
}

.water-chart canvas {
    width: 100% !important; /* Makes the canvas take full width */
    height: 400px !important; /* Increases the height of the chart */
}



    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');
    *{
        margin: 0;
        padding: 0; 
        box-sizing: border-box; 
        font-family: sans-serif;
    }
    h3{
        color:rgb(228, 228, 242);
        font-weight:800 ;
        text-align: center;
        padding: 10px 0;
        pointer-events: none;
    }
    ul li{
        color: rgb(229, 222, 222);
        list-style: none;
    }
    
    ul li a{
        color: rgb(229, 222, 222);
        font-weight: 500;
        padding: 5px 0;
        display: block;
        text-transform: capitalize;
        text-decoration: none;
        transform: 0.2s ease-bout;
    }
    
    ul li:hover{
        background-color: rgb(236, 236, 236);
        border-radius: 5px;
    }
    ul li:hover a{
        color: rgb(23, 5, 138);

    }
    
    ul li a i{
        width: 40px;
        text-align: center;
    }
    
    input{
        display: none;
        visibility: hidden;
        -webkit-appearance: none;
        appearance: none;
    }
    .toggle {
        position: absolute; /* Position relative to the slide menu */
        height: 30px;
        width: 30px;
        top: 20px;
        left: 15px;
        cursor: pointer;
        border-radius: 2px;
        background-color: #052f5f;
        z-index: 1001;
        display: block;
        visibility: visible;
    }
    
   
    .toggle .top_line{
        top: 25%;
        left: 50%;
        transform: translate(-50%,-50%);
    }
    .toggle .middle_line{
        top: 45%;
        left: 50%;
        transform: translate(-50%,-50%);
    }
    .toggle .bottom_line{
        top: 65%;
        left: 50%;
        transform: translate(-50%,-50%);
    }
    input:checked ~ .toggle .top_line{
        left: 7.5px;
        top: 16px;
        width: 25px;
        transform: rotate(45deg);
    }
    input:checked ~ .toggle .bottom_line{
        left: 7.5px;
        top: 16px;
        width: 25px;
        transform: rotate(-45deg);
    }
    input:checked ~ .toggle .middle_line{
        opacity: 0;
        transform: translateX(20px);
    }
    input:checked ~ .slide{
        transform: translateX(0);
        
    }
    
    .container h2:hover {
        background: linear-gradient(45deg, #2f4f7f, #4682b4, #2f4f7f);
        color: white;
    }
    
    
    h2 {
        text-align: center;
        color: #052f5f;
        margin-bottom: 10px;
        font-size: 2.5rem;
        font-weight: bold;
        border: 4px solid #052f5f;
        padding: 5px;
        background-color: #f1f1f1;
    }
    
     /* Responsive Design */
     @media (max-width: 768px) {
         .chart-box1,
         .chart-box2 {
             padding: 10px;
         }
     }

     .modal-content {
         background-color: white;
         margin: 15% auto;
         padding: 20px;
         border: 1px solid #888;
         width: 50%;
         border-radius: 10px;
     }
     
     .modal-content h2 {
         color: #052f5f;
         text-align: center;
     }
     .modal-content ul {
         list-style-type: disc;
         margin: 10px 0;
         padding-left: 20px;
     }
     
     .modal-content .close {
         color: #aaa;
         float: right;
         font-size: 28px;
         font-weight: bold;
         cursor: pointer;
     }
     
     .modal-content .close:hover,
     .modal-content .close:focus {
         color: black;
         text-decoration: none;
     }
    
    .footer-container a:hover {
        text-decoration: underline;
        color: #d1ecf1;
    }

.slide ul{
    position: fixed;
}

/* Chart Hovers*/

.chart-section:hover{
    background-color: #dfdfdf41;
    transform: scale(1.05);
    transition: transform 0.3s ease;
}



/* Menu Icon (Round Button) */
.toggle {
    position: fixed; /* Fix the icon in the top-left corner */
    top: 10px; /* Adjust distance from the top */
    left: 18px; /* Adjust distance from the left */
    height: 40px; /* Size of the round button */
    width: 40px; /* Size of the round button */
    background-color: #052f5f; /* Dark blue background */
    border-radius: 50%; /* Make it round */
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1001; /* Ensure it stays above other content */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); /* Add a subtle shadow */
    transition: background-color 0.3s ease; /* Smooth hover effect */
    transition: top 0.3s ease;
}

/* Hover Effect for the Menu Icon */
.toggle:hover {
    background-color: white;
}
.toggle:hover .common{
    background-color: #052f5f;
}
/* Toggle Lines (Hamburger Icon) */
.toggle .common {
    position: absolute;
    height: 4px;
    width: 25px;
    background-color: white; /* White lines for the hamburger icon */
    border-radius: 50px ;
    transition: 0.3s ease;
    margin: 3px 0; /* Space between lines */
}

/* Slide Menu */
.slide {
    height: 100vh; /* Full height of the viewport */
    width: 180px;
    position: fixed; /* Fix the slide menu */
    top: 5%;
    left: 0;
    background-color: #052f5f; /* Dark blue color */
    transition: 0.6s ease;
    transform: translateX(-180px); /* Hide the menu by default */
    z-index: 1000; /* Ensure it stays above other content */
}

/* Slide Menu Open State */
input:checked ~ .slide {
    transform: translateX(0); /* Show the slide menu */
}

/* Adjust the Header to Account for the Fixed Menu Icon */
.header {
    background-color: #052f5f;
    color: white;
    padding: 30px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999; /* Ensure it stays below the menu icon */
    margin-left: 0px; /* Adjust for the slide menu width */
    transition: top 0.3s ease;
}

.logout-button {
    background-color: #052f5f;
    color: white;
    border: none;
    padding: 10px 15px;
    font-size: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: absolute;
    top: 10px;
    right: 10px;
    border-radius: 8px;
    background-color: none;
}

.logout-button i {
    margin-right: 5px;
}

.logout-button:hover {
    background-color: #034f8f;
}

.logout-button:active {
    transform: scale(0.9);
}

.container {
    max-width: 1200px;
    margin: 50px auto;
    margin-top: 80px;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

footer {
    background-color: #052f5f;
    color: white;
    padding: 20px 0;
    position: relative;
    bottom: 0;
    width: 100%;
    text-align: center;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #ccc;
}

.footer-logo {
    margin-right: 20px;
}

.footer-logo h2 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.footer-logo p {
    font-size: 14px;
    color: #ccc;
}

.footer-social-links {
    margin-left: 20px;
}

.footer-social-links h3 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.footer-social-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-social-links ul li {
    margin-bottom: 10px;
}

.footer-social-links ul li a {
    color: white;
    text-decoration: none;
    font-size: 16px;
    transition: color 0.3s ease;
}

.footer-social-links h3:hover, .footer-social-links ul li a:hover {
    text-decoration: none;
}

.footer-bottom {
    margin-top: 20px;
}

.footer-bottom p {
    font-size: 14px;
    color: #ccc;
}

footer li:hover{
    transform: scale(1.1);
    transition: transform 0.3s ease;
    background-color: #052f5f;
}

.home-button {
    position: fixed;
    bottom: 30px;
    left: 25px;
    background-color: #052f5f;
    color: #ffffff;
    border: none;
    border-radius: 50%;
    padding: 15px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1000;
}

.home-button i {
    font-size: 24px;
}

.home-button:hover {
    transform: scale(1.2);
    background-color: #0a3d8a;
}

.savings-section {
    background-color: #f7f7f7;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.savings-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.water-savings, .electricity-savings {
    text-align: center;
    width: 45%;
}

.water-savings h2, .electricity-savings h2 {
    color: #3498db;
    font-size: 24px;
    margin-bottom: 10px;
}

.water-savings p, .electricity-savings p {
    font-size: 18px;
    margin-bottom: 10px;
}

#water-savings-amount, #electricity-savings-amount {
    font-size: 36px;
    font-weight: bold;
    color: #ffffff;
}

#water-savings-percentage, #electricity-savings-percentage {
    font-size: 24px;
    color: #ffffff;
    
}

.water-savings,.electricity-savings {
    text-align: center;
    width: 45%;
    background-color: #086bb7;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.water-savings h2,.electricity-savings h2 {
    color: #052f5f;
    font-size: 24px;
    margin-bottom: 10px;
}


.water-savings p {
    font-size: 18px;
    margin-bottom: 10px;
    color: #ffffff; 
}


.water-savings i,.electricity-savings i {
    font-size: 36px;
    margin-bottom: 10px;
    color: #052f5f; 
}

.water-savings:hover,.electricity-savings:hover {
    background-color: #052f5f;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);

}


.water-savings:hover, .electricity-savings:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);

}

.water-savings:hover h2, .electricity-savings:hover h2{
    background: linear-gradient(45deg, #2f4f7f, #4682b4, #2f4f7f);
    color: white;
}


.water-savings:hover i, .electricity-savings:hover i {
    color: #086bb7;
}
