<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Restroom System Monitoring</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.17.2/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.17.2/firebase-database.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: #f7f7f7;
        }

        .chart-section {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .button-group {
            margin-bottom: 15px;
        }

        .button-group button {
            background: #007bff;
            color: #fff;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .button-group button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="chart-section">
        <h3 style="color: black;">Power Consumption</h3>

        <div class="button-group">
            <button onclick="updateChart('day')">Day</button>
            <button onclick="updateChart('month')">Month</button>
            <button onclick="updateChart('year')">Year</button>
        </div>
        <canvas id="powerConsumptionChart"></canvas>
    </div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/9.17.2/firebase-app.js";
        import { getDatabase, ref, get, child } from "https://www.gstatic.com/firebasejs/9.17.2/firebase-database.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyAnU69sqRHEC9xDj7sQkyBH_sOG3AOhF24",
            authDomain: "sterilease-b1185.firebaseapp.com",
            databaseURL: "https://sterilease-b1185-default-rtdb.asia-southeast1.firebasedatabase.app",
            projectId: "sterilease-b1185",
            storageBucket: "sterilease-b1185.firebasestorage.app",
            messagingSenderId: "287619655835",
            appId: "1:287619655835:web:3a7bf5fa9372d8bef33e3e"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const database = getDatabase(app);

        // Chart.js Initialization
        const ctx = document.getElementById("powerConsumptionChart").getContext("2d");
        let powerChart;

        const initializeChart = (labels, data) => {
            if (powerChart) powerChart.destroy(); // Destroy the existing chart if any

            powerChart = new Chart(ctx, {
                type: "line",
                data: {
                    labels: labels,
                    datasets: [{
                        label: "Power Consumption (kWh)",
                        data: data,
                        borderColor: "rgba(0, 123, 255, 0.8)",
                        backgroundColor: "rgba(0, 123, 255, 0.3)",
                        borderWidth: 2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: "top"
                        },
                        tooltip: {
                            mode: "index",
                            intersect: false
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: "Date"
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: "Power (kWh)"
                            }
                        }
                    }
                }
            });
        };

        // Fetch data from Firebase
        const fetchData = async (period) => {
            try {
                const dbRef = ref(database);
                const snapshot = await get(child(dbRef, "/users/OYMxBTGPGnclvrq8O0nYYOEJPDA3/data"));
                if (snapshot.exists()) {
                    const data = snapshot.val();
                    const labels = [];
                    const powerData = [];

                    Object.keys(data).forEach((key) => {
                        const date = new Date(key); // Convert key (timestamp) to date object
                        const now = new Date();

                        // Filter based on selected period
                        if (
                            (period === "day" && date.getDay() === now.getDay()) ||
                            (period === "month" && date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear()) ||
                            (period === "year" && date.getFullYear() === now.getFullYear())
                        ) {
                            labels.push(key); // Add timestamp
                            powerData.push(data[key].power || 0); // Add power value
                        }
                    });

                    return { labels, powerData };
                } else {
                    console.error("No data available.");
                    return { labels: [], powerData: [] };
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                return { labels: [], powerData: [] };
            }
        };

        // Update Chart
        window.updateChart = async (period) => {
            const { labels, powerData } = await fetchData(period);
            initializeChart(labels, powerData);
        };
        

        // Default chart load
        updateChart("day");
    </script>
</body>
</html>