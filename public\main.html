
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Restroom System Monitoring</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/9.15.0/firebase-app.js";
        import { getDatabase, ref, onValue } from "https://www.gstatic.com/firebasejs/9.15.0/firebase-database.js";
    
        const firebaseConfig = {
            apiKey: "AIzaSyAnU69sqRHEC9xDj7sQkyBH_sOG3AOhF24",
            authDomain: "sterilease-b1185.firebaseapp.com",
            databaseURL: "https://sterilease-b1185-default-rtdb.asia-southeast1.firebasedatabase.app",
            projectId: "sterilease-b1185",
            storageBucket: "sterilease-b1185.appspot.com",
            messagingSenderId: "287619655835",
            appId: "1:287619655835:web:5f188535e492ebc3f33e3e"
        };
    
        const app = initializeApp(firebaseConfig);
        const database = getDatabase(app);
        window.database = database;
        window.ref = ref;
        window.onValue = onValue;
    </script>
    <link rel="stylesheet" href="main.css">
</head>
<body>
    <div class="header">
        <button class="logout-button" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
        </button>
    </div>
    <label>
        <input type="checkbox">
        <div class="toggle">
            <span class="top_line common"></span>
            <span class="middle_line common"></span>
            <span class="bottom_line common"></span>
        </div>
        <div class="slide">
            <h3>MENU</h3>
            <ul>
                <li><a href="#"><i class="fas fa-tv"></i>Dashboard</li>
                <li><a href="javascript:void(0);" onclick="openPopup()"><i class="far fa-user"></i>About</li>
                <li><a href="Maintain.html"><i class="fab fa-gripfire"></i>Maintain</li>
                <li onclick="openModal()"><a href="#"><i class="far fa-comments"></i>analyze</li>
                <li onclick="logout()"><a href="#">
                <i class="fas fa-sign-out-alt"></i> logout
                </li>
            </ul>
        </div>
    </label>
    <div class="popup" id="aboutPopup">
        <div class="popup-content">
            <span class="close" onclick="closePopup()">&times;</span>
            <h2 class="popup-title">About SterilEase</span></h2>
            <p class="popup-description">
                SterilEase is a state-of-the-art smart restroom system designed to revolutionize hygiene standards in public spaces. With touchless technology and cutting-edge monitoring tools, it ensures cleanliness, efficiency, and sustainability.
            </p>
            <h3 class="features-title">Key Features</h3>
            <ul class="features-list">
                <li style="color: black;"><i class="fas fa-sun"></i> UV Sterilization</li>
                <li style="color: black;"><i class="fas fa-fan"></i> Vacuum Pump Monitoring</li>
                <li style="color: black;"><i class="fas fa-toilet"></i> Septic Tank Management</li>
                <li style="color: black;"><i class="fas fa-tint"></i> Water and Power Consumption Tracking</li>
            </ul>
        </div>
    </div>
    <!-- Modal for Analyze -->
    <div id="analyzeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h1 class="modal-title">Analyze and Visualize Your Data</h1>
            <p class="modal-intro">
                Welcome to the Analyze section of <strong>SterilEase</strong>. This page is designed to help you visualize and monitor data related to water usage and power consumption over time.
            </p>
    
            <div class="modal-body">
                <img src="/assests/graph1.jpg" alt="Visualization Example" class="modal-image">
                <div class="modal-description">
                    <p>
                        By leveraging interactive graphs, you can track patterns and trends, ensuring efficient management of these vital resources.
                    </p>
                    <p>
                        Use the provided charts to gain insights into resource usage and make informed decisions to optimize operations, reduce waste, and promote sustainability.
                    </p>
                    <p>
                        These tools empower you to take control of your resource management with clarity and precision.
                    </p>
                </div>
            </div>
    
            <h2 class="tools-title">Tools and Features</h2>
            <div class="tools-container">
                <div class="tool">
                    <img src="/assests/charts.jpg" alt="Graph Icon">
                    <p>Interactive Graphs</p>
                </div>
                <div class="tool">
                    <img src="/assests/data insight.jpg" alt="Insights Icon">
                    <p>Data Insights</p>
                </div>
                <div class="tool">
                    <img src="/assests/resource.webp" alt="Management Icon">
                    <p>Resource Management</p>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <h2>SterilEase </h2>
        <!-- Important Section for Water and Electricity Savings -->
        <div class="savings-section">
            <div class="savings-container">
                <div class="water-savings">
                    <i class="fas fa-tint"></i>
                    <h2>Water Savings</h2>
                    <p id="water-savings-amount">0 Liters</p>
                    <p id="water-savings-percentage">0%</p>
                </div>
                
                <div class="electricity-savings">
                    <i class="fas fa-bolt"></i>
                    <h2>Electricity Savings</h2>
                    <p id="electricity-savings-amount">0 kWh</p>
                    <p id="electricity-savings-percentage">0%</p>
                </div>
            </div>
        </div>
        <main>
        <!-- Power Consumption Chart -->
        <div class="chart-section power-chart">
            <h3 style="color: darkblue;">Power Consumption</h3>

            <div class="button-group">
                <button onclick="updateChart('day', 'power')">Day</button>
                <button onclick="updateChart('month', 'power')">Month</button>
                <button onclick="updateChart('year', 'power')">Year</button>
            </div>
            <canvas id="powerConsumptionChart"></canvas>
            
        </div>

        <!-- Predictive Maintenance and Septic Tank in Same Container -->
        <div class="chart-section septic-maintenance-container">
            <!-- Predictive Maintenance -->
            <div class="predictive-box-container">
                <h3 style="color: darkblue;">Predictive Maintenance</h3>
                <div class="predictive-box">
                    <div class="fill" id="fillBar"></div>
                    <div class="threshold-line"></div>
                    <div class="alert-message" id="alertMessage"></div>
                </div>
                <button onclick="updatePredictiveGraph()">Update</button>
            </div>

            <script type="module">
                // Function to update the Predictive Maintenance UI
                function updatePredictiveGraph() {
                  onValue(predictiveRef, (snapshot) => {
                    const data = snapshot.val();
              
                    if (data) {
                      const { currentValue, threshold } = data;
              
                      // Update the fill bar width based on currentValue
                      const fillBar = document.getElementById("fillBar");
                      fillBar.style.width = `${currentValue}%`;
              
                      // Display alert message if threshold is reached
                      const alertMessage = document.getElementById("alertMessage");
                      if (currentValue >= threshold) {
                        alertMessage.style.display = "block";
                      } else {
                        alertMessage.style.display = "none";
                      }
                    }
                  });
                }
              
                // Initial call to updatePredictiveGraph
                updatePredictiveGraph();
              </script>

            <!-- Septic Tank -->
            <div class="chart-container">
                <h3 style="color: darkblue; ">Septic Tank</h3>
                <canvas id="resourcePieChart" style="vertical-align: middle; justify-content: center;"></canvas>
            </div>
        </div>

        <!-- Flushes Chart -->
        <div class="chart-section flushes-chart">
            <h3 style="color: darkblue;">Number of Flushes</h3>
            <div class="button-group">
                <button onclick="updateChart('day', 'flushes')">Day</button>
                <button onclick="updateChart('month', 'flushes')">Month</button>
                <button onclick="updateChart('year', 'flushes')">Year</button>
            </div>
            <canvas id="flushesChart"></canvas>
            
        </div>
        <!-- Water Consumption Chart -->
        <div class="chart-section water-chart">
            <h3 style="color: darkblue;">Water Consumption</h3>
            <div class="button-group">
                <button onclick="updateChart('day', 'water')">Day</button>
                <button onclick="updateChart('month', 'water')">Month</button>
                <button onclick="updateChart('year', 'water')">Year</button>
            </div>
            <canvas id="waterConsumptionChart"></canvas>
        </div>

        </main>
    </div>
    <footer>
        <div class="footer-container">
            <div class="footer-top">
                <div class="footer-logo">
                    <h2>SterilEase</h2>
                    <p>Copyright &copy; 2025 SterilEase. All rights reserved.</p>
                </div>
                <div class="footer-social-links">
                    <h3>Stay Connected</h3>
                    <ul>
                        <li>
                            <a href="https://www.instagram.com/vikash_s_nair/" target="_blank">
                                <i class="fa fa-instagram" aria-hidden="true"></i>
                                Instagram
                            </a>
                        </li>
                        <li>
                            <a href="mailto:<EMAIL>">
                                <i class="fa fa-envelope" aria-hidden="true"></i>
                                Email
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Designed and Developed by SterilEase</p>
            </div>
        </div>
    </footer>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <!-- Including Chart.js -->
    <script src="main.js"></script>
</body>
</html>
