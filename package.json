{"name": "se_vikash", "version": "1.0.0", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"electron-squirrel-startup": "^1.0.1", "express": "^4.21.2", "firebase": "^11.1.0"}, "devDependencies": {"@electron-forge/cli": "^7.6.0", "@electron-forge/maker-deb": "^7.6.0", "@electron-forge/maker-rpm": "^7.6.0", "@electron-forge/maker-squirrel": "^7.6.0", "@electron-forge/maker-zip": "^7.6.0", "@electron-forge/plugin-auto-unpack-natives": "^7.6.0", "@electron-forge/plugin-fuses": "^7.6.0", "@electron/fuses": "^1.8.0", "electron": "^33.2.1", "electron-packager": "^17.1.2"}}