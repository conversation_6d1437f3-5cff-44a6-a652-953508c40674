
// Function to open the popup
function openPopup() {
    document.getElementById("aboutPopup").style.display = "block";
}

// Function to close the popup
function closePopup() {
    document.getElementById("aboutPopup").style.display = "none";
}

function logout() {
    // Redirect to the login page
    window.location.href = 'resigter.html';
}

function openModal() {
    document.getElementById('analyzeModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('analyzeModal').style.display = 'none';
}

// Close modal when clicking outside the content
window.onclick = function(event) {
    const modal = document.getElementById('analyzeModal');
    if (event.target === modal) {
        closeModal();
    }
};


// Close modal when clicking outside the content
window.onclick = function(event) {
    const modal = document.getElementById('analyzeModal');
    if (event.target === modal) {
        closeModal();
    }
};



// Single declaration of shared functions
function updatePredictiveGraph() {
    const fillBar = document.getElementById('fillBar');
    const alertMessage = document.getElementById('alertMessage');
    const fillPercentage = Math.floor(Math.random() * 101);

    fillBar.style.width = fillPercentage + '%';

    if (fillPercentage >= 80) {
        fillBar.style.backgroundColor = '#DC143C';
        alertMessage.style.display = 'block';
    } else if (fillPercentage >= 40) {
        fillBar.style.backgroundColor = '#ffd800';
        alertMessage.style.display = 'none';
    } else {
        fillBar.style.backgroundColor = '#03C03C';
        alertMessage.style.display = 'none';
    }
}
function updateChart(userId, timeframe, chartType) {
    event.preventDefault();
    const dataRef = db.ref(`users/${userId}/data`);
    dataRef.on('value', (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const dates = Object.keys(data);
        const filteredDates = dates.filter((date) => {
          const dateObject = new Date(date);
          const timeframeObject = new Date(timeframe);
          return dateObject >= timeframeObject;
        });
        const values = filteredDates.map((date) => data[date][chartType]);
  
        switch (chartType) {
          case 'flush_count':
            updateFlushesChart(values, filteredDates);
            break;
          case 'water':
            updateWaterChart(values, filteredDates);
            break;
          case 'power':
            updatePowerChart(values, filteredDates);
            break;
          default:
            console.error('Invalid chart type:', chartType);
        }
      }
    });
  }
// Function to update the Power Consumption chart
function updatePowerChart(data) {
    const ctx = document.getElementById('powerConsumptionChart').getContext('2d');
    if (window.powerChart) {
        // If the chart already exists, update its data
        window.powerChart.data.labels = data.labels;
        window.powerChart.data.datasets[0].data = data.values;
        window.powerChart.update();
    } else {
        // If the chart doesn't exist, create it
        window.powerChart = new Chart(ctx, {
            type: 'line', // or 'bar', 'pie', etc.
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Power Consumption',
                    data: data.values,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Function to update the Flushes chart
function updateFlushesChart(data) {
    const ctx = document.getElementById('flushesChart').getContext('2d');
    if (window.flushesChart) {
        window.flushesChart.data.labels = data.labels;
        window.flushesChart.data.datasets[0].data = data.values;
        window.flushesChart.update();
    } else {
        window.flushesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Number of Flushes',
                    data: data.values,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Function to update the Water Consumption chart
function updateWaterChart(data) {
    const ctx = document.getElementById('waterConsumptionChart').getContext('2d');
    if (window.waterChart) {
        window.waterChart.data.labels = data.labels;
        window.waterChart.data.datasets[0].data = data.values;
        window.waterChart.update();
    } else {
        window.waterChart = new Chart(ctx, {
            type: 'line', // or 'bar', 'pie', etc.
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Water Consumption',
                    data: data.values,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Function to update the Septic Tank chart
function updateSepticChart(data) {
    const ctx = document.getElementById('resourcePieChart').getContext('2d');
    if (window.septicChart) {
        window.septicChart.data.labels = data.labels;
        window.septicChart.data.datasets[ 0].data = data.values;
        window.septicChart.update();
    } else {
        window.septicChart = new Chart(ctx, {
            type: 'pie', // or 'doughnut', etc.
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Septic Tank Resources',
                    data: data.values,
                    backgroundColor: [
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)',
                        'rgba(255, 99, 132, 0.2)'
                    ],
                    borderColor: [
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Septic Tank Resource Distribution'
                    }
                }
            }
        });
    }
}
function fetchChartData(path, chart, label) {
    const db = window.database;
    const dataRef = ref(db, path);
    onValue(dataRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
            switch(path) {
                case 'FlushCount':
                    // Update flushes chart with daily data
                    chart.data.datasets[0].data = data.dailyFlushes;
                    chart.data.labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'];
                    break;
                    
                case 'PowerConsumption':
                    // Update power consumption chart with daily usage
                    chart.data.datasets[0].data = data.dailyUsage;
                    chart.data.labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'];
                    break;
                    
                case 'SepticTankStatus':
                    // Update septic tank chart
                    chart.data.datasets[0].data = [
                        100 - data.percentageFilled, // Empty portion
                        data.percentageFilled // Filled portion
                    ];
                    break;
            }
            chart.update();
        }
    });
}

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Power Consumption Chart
    const powerCtx = document.getElementById('powerConsumptionChart').getContext('2d');
    const powerConsumptionChart = new Chart(powerCtx, {
        type: 'line',
        data: {
            labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'],  // Default labels
            datasets: [{
                label: 'Power Consumption (kWh)',
                data: [0, 0, 0, 0, 0],  // Default data
                borderColor: 'rgb(8, 107, 183)',
                tension: 0.6
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Water Consumption Chart
    const waterCtx = document.getElementById('waterConsumptionChart').getContext('2d');
    const waterConsumptionChart = new Chart(waterCtx, {
        type: 'line',
        data: {
            labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'],  // Default labels
            datasets: [{
                label: 'Water Consumption (L)',
                data: [0, 0, 0, 0, 0],  // Default data
                borderColor: 'rgb(8, 107, 183)',
                tension: 0.6
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Flushes Chart
    const flushesCtx = document.getElementById('flushesChart').getContext('2d');
    const flushesChart = new Chart(flushesCtx, {
        type: 'bar',
        data: {
            labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'],  // Default labels
            datasets: [{
                label: 'Number of Flushes',
                data: [5, 8, 10, 4, 3],  // Default data
                backgroundColor: 'rgb(8, 107, 183)',
                tension: 0.6,
    
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Septic Tank Chart
    const septicCtx = document.getElementById('resourcePieChart').getContext('2d');
    const septicTankChart = new Chart(septicCtx, {
        type: 'pie',
        data: {
            labels: ['Empty', 'Full'],
            datasets: [{
                data: [70, 30],  // Default data
                backgroundColor: [
                    ' #052f5f',
                    'rgb(8, 107, 183)'
                ]
            }]
        },
        options: {
            responsive: true
        }
    });

    // Single section for all Firebase listeners
    try {
        // Log entire database structure
        const rootRef = window.ref(window.database, '/');
        window.onValue(rootRef, (snapshot) => {
            const data = snapshot.val();
            console.log('Entire Database Structure:', data);
            
            // Update individual charts based on the data
            if (data) {
                // Update Flushes Chart
                if (data.FlushCount && data.FlushCount.dailyFlushes) {
                    console.log('FlushCount Data:', data.FlushCount);
                    flushesChart.data.datasets[0].data = data.FlushCount.dailyFlushes;
                    flushesChart.update();
                }

                // Update Power Consumption Chart
                if (data.PowerConsumption && data.PowerConsumption.dailyUsage) {
                    console.log('PowerConsumption Data:', data.PowerConsumption);
                    powerConsumptionChart.data.datasets[0].data = data.PowerConsumption.dailyUsage;
                    powerConsumptionChart.update();
                }

                // Update Water Consumption Chart
if (data.WaterConsumption && data.WaterConsumption.dailyConsumption) {
console.log('Water Consumption Data Found:', {
    rawData: data.WaterConsumption,
    dailyValues: data.WaterConsumption.dailyConsumption,
    unit: data.WaterConsumption.unit
});
waterConsumptionChart.data.datasets[0].data = data.WaterConsumption.dailyConsumption;
waterConsumptionChart.update();
} else {
console.warn('Water Consumption Data Missing or Invalid:', {
    hasWaterConsumption: !!data.WaterConsumption,
    fullData: data.WaterConsumption
});
}

                // Update Septic Tank Chart
                if (data.SepticTankStatus && data.SepticTankStatus.history) {
console.log('SepticTankStatus Data:', data.SepticTankStatus);

// Get the latest value from the history array
const history = data.SepticTankStatus.history;
const latestReading = history[history.length - 1];

if (latestReading && latestReading.value !== undefined) {
    const filledPercentage = latestReading.value;
    console.log('Latest Septic Tank Value:', filledPercentage);
    
    septicTankChart.data.datasets[0].data = [
        100 - filledPercentage,  // Empty portion
        filledPercentage         // Filled portion
    ];
    septicTankChart.update();
} else {
    console.warn('No valid septic tank readings found');
}
} else {
console.warn('Septic Tank Status data missing or invalid');
}
            }
        }, (error) => {
            console.error('Error fetching database:', error);
        });
    } catch (error) {
        console.error('Error setting up Firebase listeners:', error);
    }
});


const header = document.querySelector('.header');
window.addEventListener('scroll', () => {
    // Check if the user has scrolled down
    if (window.scrollY > 50) {
        // Slide the header up
        header.style.top = '-70px';
    } else {
        // Slide the header back down
        header.style.top = '0';
    }
});

const toggle = document.querySelector('.toggle');
window.addEventListener('scroll', () => {
    // Check if the user has scrolled down
    if (window.scrollY > 50) {
        // Slide the toggle up
        toggle.style.top = '70px';

    } else {
        // Slide the toggle back down
        toggle.style.top = '15px';
    }
});


  document.querySelectorAll('footer h2, footer p').forEach(function(element) {
    element.addEventListener('click', function(event) {
      event.preventDefault();
    });
  });


  // Function to calculate water savings
function calculateWaterSavings() {
    const waterUsagePerFlushSterilease = 2; // liters
    const waterUsagePerFlushNormalRestroom = 8; // liters
    const totalFlushes = data.FlushCount.dailyFlushes.reduce((a, b) => a + b, 0);

    const waterSavings = (waterUsagePerFlushNormalRestroom - waterUsagePerFlushSterilease) * totalFlushes;
    const waterSavingsPercentage = ((waterUsagePerFlushNormalRestroom - waterUsagePerFlushSterilease) / waterUsagePerFlushNormalRestroom) * 100;

    return { amount: waterSavings, percentage: waterSavingsPercentage };
}

// Function to calculate electricity savings
function calculateElectricitySavings() {
    const electricityUsagePerFlushSterilease = 0.2; // kWh
    const electricityUsagePerFlushNormalRestroom = 0.5; // kWh
    const totalFlushes = data.FlushCount.dailyFlushes.reduce((a, b) => a + b, 0);

    const electricitySavings = (electricityUsagePerFlushNormalRestroom - electricityUsagePerFlushSterilease) * totalFlushes;
    const electricitySavingsPercentage = ((electricityUsagePerFlushNormalRestroom - electricityUsagePerFlushSterilease) / electricityUsagePerFlushNormalRestroom) * 100;

    return { amount: electricitySavings, percentage: electricitySavingsPercentage };
}

// Function to update the savings section
function updateSavingsSection() {
    const waterSavings = calculateWaterSavings();
    const electricitySavings = calculateElectricitySavings();

    document.getElementById('water-savings-amount').innerText = `${waterSavings.amount} Liters`;
    document.getElementById('water-savings-percentage').innerText = `${waterSavings.percentage}%`;
    document.getElementById('electricity-savings-amount').innerText = `${electricitySavings.amount} kWh`;
    document.getElementById('electricity-savings-percentage').innerText = `${electricitySavings.percentage}%`;
}

// Call the updateSavingsSection function whenever the data changes
onValue(ref(database, 'FlushCount'), (snapshot) => {
    const data = snapshot.val();
    updateSavingsSection();
});