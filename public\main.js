
// Function to open the popup
function openPopup() {
    document.getElementById("aboutPopup").style.display = "block";
}

// Function to close the popup
function closePopup() {
    document.getElementById("aboutPopup").style.display = "none";
}

function logout() {
    // Redirect to the login page
    window.location.href = 'resigter.html';
}

function openModal() {
    document.getElementById('analyzeModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('analyzeModal').style.display = 'none';
}

// Close modal when clicking outside the content
window.onclick = function(event) {
    const modal = document.getElementById('analyzeModal');
    if (event.target === modal) {
        closeModal();
    }
};


// Close modal when clicking outside the content
window.onclick = function(event) {
    const modal = document.getElementById('analyzeModal');
    if (event.target === modal) {
        closeModal();
    }
};



// Single declaration of shared functions
function updatePredictiveGraph() {
    const fillBar = document.getElementById('fillBar');
    const alertMessage = document.getElementById('alertMessage');
    const fillPercentage = Math.floor(Math.random() * 101);

    fillBar.style.width = fillPercentage + '%';

    if (fillPercentage >= 80) {
        fillBar.style.backgroundColor = '#DC143C';
        alertMessage.style.display = 'block';
    } else if (fillPercentage >= 40) {
        fillBar.style.backgroundColor = '#ffd800';
        alertMessage.style.display = 'none';
    } else {
        fillBar.style.backgroundColor = '#03C03C';
        alertMessage.style.display = 'none';
    }
}
function updateChart(userId, timeframe, chartType) {
    event.preventDefault();
    const dataRef = db.ref(`users/${userId}/data`);
    dataRef.on('value', (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const dates = Object.keys(data);
        const filteredDates = dates.filter((date) => {
          const dateObject = new Date(date);
          const timeframeObject = new Date(timeframe);
          return dateObject >= timeframeObject;
        });
        const values = filteredDates.map((date) => data[date][chartType]);
  
        switch (chartType) {
          case 'flush_count':
            updateFlushesChart(values, filteredDates);
            break;
          case 'water':
            updateWaterChart(values, filteredDates);
            break;
          case 'power':
            updatePowerChart(values, filteredDates);
            break;
          default:
            console.error('Invalid chart type:', chartType);
        }
      }
    });
  }
// Function to update the Power Consumption chart
function updatePowerChart(data) {
    const ctx = document.getElementById('powerConsumptionChart').getContext('2d');
    if (window.powerChart) {
        // If the chart already exists, update its data
        window.powerChart.data.labels = data.labels;
        window.powerChart.data.datasets[0].data = data.values;
        window.powerChart.update();
    } else {
        // If the chart doesn't exist, create it
        window.powerChart = new Chart(ctx, {
            type: 'line', // or 'bar', 'pie', etc.
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Power Consumption',
                    data: data.values,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Function to update the Flushes chart
function updateFlushesChart(data) {
    const ctx = document.getElementById('flushesChart').getContext('2d');
    if (window.flushesChart) {
        window.flushesChart.data.labels = data.labels;
        window.flushesChart.data.datasets[0].data = data.values;
        window.flushesChart.update();
    } else {
        window.flushesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Number of Flushes',
                    data: data.values,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Function to update the Water Consumption chart
function updateWaterChart(data) {
    const ctx = document.getElementById('waterConsumptionChart').getContext('2d');
    if (window.waterChart) {
        window.waterChart.data.labels = data.labels;
        window.waterChart.data.datasets[0].data = data.values;
        window.waterChart.update();
    } else {
        window.waterChart = new Chart(ctx, {
            type: 'line', // or 'bar', 'pie', etc.
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Water Consumption',
                    data: data.values,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Function to update the Septic Tank chart
function updateSepticChart(data) {
    const ctx = document.getElementById('resourcePieChart').getContext('2d');
    if (window.septicChart) {
        window.septicChart.data.labels = data.labels;
        window.septicChart.data.datasets[ 0].data = data.values;
        window.septicChart.update();
    } else {
        window.septicChart = new Chart(ctx, {
            type: 'pie', // or 'doughnut', etc.
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Septic Tank Resources',
                    data: data.values,
                    backgroundColor: [
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)',
                        'rgba(255, 99, 132, 0.2)'
                    ],
                    borderColor: [
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Septic Tank Resource Distribution'
                    }
                }
            }
        });
    }
}
function fetchChartData(path, chart, label) {
    const db = window.database;
    const dataRef = ref(db, path);
    onValue(dataRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
            switch(path) {
                case 'FlushCount':
                    // Update flushes chart with daily data
                    chart.data.datasets[0].data = data.dailyFlushes;
                    chart.data.labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'];
                    break;
                    
                case 'PowerConsumption':
                    // Update power consumption chart with daily usage
                    chart.data.datasets[0].data = data.dailyUsage;
                    chart.data.labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'];
                    break;
                    
                case 'SepticTankStatus':
                    // Update septic tank chart
                    chart.data.datasets[0].data = [
                        100 - data.percentageFilled, // Empty portion
                        data.percentageFilled // Filled portion
                    ];
                    break;
            }
            chart.update();
        }
    });
}

// Sample Data Generator Functions
function generateSampleData() {
    const currentDate = new Date();
    const sampleData = {
        // Generate realistic daily data for the past 7 days
        daily: {
            labels: [],
            powerConsumption: [],
            waterConsumption: [],
            flushCounts: []
        },
        // Generate monthly data for the past 12 months
        monthly: {
            labels: [],
            powerConsumption: [],
            waterConsumption: [],
            flushCounts: []
        },
        // Generate yearly data for the past 5 years
        yearly: {
            labels: [],
            powerConsumption: [],
            waterConsumption: [],
            flushCounts: []
        }
    };

    // Generate daily data (past 7 days)
    for (let i = 6; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setDate(date.getDate() - i);
        sampleData.daily.labels.push(date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }));

        // Realistic power consumption (2-8 kWh per day for a smart restroom)
        sampleData.daily.powerConsumption.push((Math.random() * 6 + 2).toFixed(1));

        // Realistic water consumption (50-200L per day)
        sampleData.daily.waterConsumption.push(Math.floor(Math.random() * 150 + 50));

        // Realistic flush counts (15-45 flushes per day)
        sampleData.daily.flushCounts.push(Math.floor(Math.random() * 30 + 15));
    }

    // Generate monthly data (past 12 months)
    for (let i = 11; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setMonth(date.getMonth() - i);
        sampleData.monthly.labels.push(date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }));

        // Monthly power consumption (60-240 kWh)
        sampleData.monthly.powerConsumption.push((Math.random() * 180 + 60).toFixed(1));

        // Monthly water consumption (1500-6000L)
        sampleData.monthly.waterConsumption.push(Math.floor(Math.random() * 4500 + 1500));

        // Monthly flush counts (450-1350 flushes)
        sampleData.monthly.flushCounts.push(Math.floor(Math.random() * 900 + 450));
    }

    // Generate yearly data (past 5 years)
    for (let i = 4; i >= 0; i--) {
        const year = currentDate.getFullYear() - i;
        sampleData.yearly.labels.push(year.toString());

        // Yearly power consumption (720-2880 kWh)
        sampleData.yearly.powerConsumption.push((Math.random() * 2160 + 720).toFixed(1));

        // Yearly water consumption (18000-72000L)
        sampleData.yearly.waterConsumption.push(Math.floor(Math.random() * 54000 + 18000));

        // Yearly flush counts (5400-16200 flushes)
        sampleData.yearly.flushCounts.push(Math.floor(Math.random() * 10800 + 5400));
    }

    return sampleData;
}

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Generate sample data
    const sampleData = generateSampleData();
    let currentTimeframe = 'daily'; // Default timeframe

    // Power Consumption Chart
    const powerCtx = document.getElementById('powerConsumptionChart').getContext('2d');
    const powerConsumptionChart = new Chart(powerCtx, {
        type: 'line',
        data: {
            labels: sampleData.daily.labels,
            datasets: [{
                label: 'Power Consumption (kWh)',
                data: sampleData.daily.powerConsumption,
                borderColor: 'rgb(8, 107, 183)',
                backgroundColor: 'rgba(8, 107, 183, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(8, 107, 183)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + ' kWh';
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Water Consumption Chart
    const waterCtx = document.getElementById('waterConsumptionChart').getContext('2d');
    const waterConsumptionChart = new Chart(waterCtx, {
        type: 'line',
        data: {
            labels: sampleData.daily.labels,
            datasets: [{
                label: 'Water Consumption (L)',
                data: sampleData.daily.waterConsumption,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(54, 162, 235)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + ' L';
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Flushes Chart
    const flushesCtx = document.getElementById('flushesChart').getContext('2d');
    const flushesChart = new Chart(flushesCtx, {
        type: 'bar',
        data: {
            labels: sampleData.daily.labels,
            datasets: [{
                label: 'Number of Flushes',
                data: sampleData.daily.flushCounts,
                backgroundColor: 'rgba(75, 192, 192, 0.8)',
                borderColor: 'rgb(75, 192, 192)',
                borderWidth: 2,
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + ' flushes';
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });

    // Septic Tank Chart with realistic data
    const septicCtx = document.getElementById('resourcePieChart').getContext('2d');
    const septicTankChart = new Chart(septicCtx, {
        type: 'pie',
        data: {
            labels: ['Available Capacity', 'Used Capacity'],
            datasets: [{
                data: [65, 35], // 35% full, 65% available
                backgroundColor: [
                    '#052f5f',
                    'rgb(8, 107, 183)'
                ],
                borderColor: '#fff',
                borderWidth: 3,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + '%';
                        }
                    }
                }
            }
        }
    });

    // Enhanced updateChart function with smooth animations and button state management
    window.updateChart = function(timeframe, chartType) {
        currentTimeframe = timeframe;

        // Update button states for visual feedback
        updateButtonStates(timeframe, chartType);

        let labels, data;

        switch(timeframe) {
            case 'day':
                labels = sampleData.daily.labels;
                break;
            case 'month':
                labels = sampleData.monthly.labels;
                break;
            case 'year':
                labels = sampleData.yearly.labels;
                break;
            default:
                labels = sampleData.daily.labels;
        }

        // Update only the specific chart with smooth animation
        switch(chartType) {
            case 'power':
                data = timeframe === 'day' ? sampleData.daily.powerConsumption :
                       timeframe === 'month' ? sampleData.monthly.powerConsumption :
                       sampleData.yearly.powerConsumption;

                updateSingleChart(powerConsumptionChart, labels, data, 'Power Consumption');
                break;

            case 'water':
                data = timeframe === 'day' ? sampleData.daily.waterConsumption :
                       timeframe === 'month' ? sampleData.monthly.waterConsumption :
                       sampleData.yearly.waterConsumption;

                updateSingleChart(waterConsumptionChart, labels, data, 'Water Consumption');
                break;

            case 'flushes':
                data = timeframe === 'day' ? sampleData.daily.flushCounts :
                       timeframe === 'month' ? sampleData.monthly.flushCounts :
                       sampleData.yearly.flushCounts;

                updateSingleChart(flushesChart, labels, data, 'Flush Count');
                break;
        }

        // Log the update for debugging
        console.log(`Chart updated: ${chartType} - ${timeframe} view`);

        // Return false to prevent any default behavior
        return false;
    };

    // Function to update button states for visual feedback
    function updateButtonStates(timeframe, chartType) {
        // Find all buttons for this chart type
        const chartSection = document.querySelector(`.${chartType}-chart`) ||
                            document.querySelector('.chart-section');

        if (chartSection) {
            const buttons = chartSection.querySelectorAll('.button-group button');

            // Remove active class from all buttons in this group
            buttons.forEach(btn => {
                btn.classList.remove('active');
                btn.style.backgroundColor = '#0a3d8a';
                btn.style.transform = 'none';
            });

            // Add active class to clicked button
            buttons.forEach(btn => {
                if (btn.textContent.toLowerCase() === timeframe.toLowerCase()) {
                    btn.classList.add('active');
                    btn.style.backgroundColor = '#052f5f';
                    btn.style.transform = 'scale(0.95)';
                    btn.style.boxShadow = 'inset 0 2px 4px rgba(0,0,0,0.3)';
                }
            });
        }
    }

    // Function to update a single chart with smooth animation
    function updateSingleChart(chart, labels, data, chartName) {
        if (!chart) {
            console.warn(`Chart not found: ${chartName}`);
            return;
        }

        // Add loading state visual feedback
        const canvas = chart.canvas;
        const container = canvas.parentElement;

        // Add subtle loading indicator
        container.style.opacity = '0.7';
        container.style.transition = 'opacity 0.2s ease';

        // Update chart data
        chart.data.labels = labels;
        chart.data.datasets[0].data = data;

        // Use smooth animation for the update
        chart.update('active', {
            duration: 800,
            easing: 'easeInOutQuart'
        });

        // Remove loading state after animation
        setTimeout(() => {
            container.style.opacity = '1';
        }, 300);

        console.log(`${chartName} chart updated successfully with ${labels.length} data points`);
    };

    // Add sample data to savings calculations
    function updateSavingsWithSampleData() {
        // Calculate based on current daily flush data
        const totalFlushes = sampleData.daily.flushCounts.reduce((a, b) => parseInt(a) + parseInt(b), 0);

        // Water savings calculation (SterilEase uses 2L vs normal 8L per flush)
        const waterSavingsAmount = (8 - 2) * totalFlushes;
        const waterSavingsPercentage = ((8 - 2) / 8 * 100).toFixed(1);

        // Electricity savings calculation (SterilEase uses 0.2kWh vs normal 0.5kWh per flush)
        const electricitySavingsAmount = ((0.5 - 0.2) * totalFlushes).toFixed(1);
        const electricitySavingsPercentage = ((0.5 - 0.2) / 0.5 * 100).toFixed(1);

        // Update the UI
        document.getElementById('water-savings-amount').innerText = `${waterSavingsAmount} Liters`;
        document.getElementById('water-savings-percentage').innerText = `${waterSavingsPercentage}%`;
        document.getElementById('electricity-savings-amount').innerText = `${electricitySavingsAmount} kWh`;
        document.getElementById('electricity-savings-percentage').innerText = `${electricitySavingsPercentage}%`;
    }

    // Initialize savings display
    updateSavingsWithSampleData();

    // Add enhanced button event listeners for better UX
    function initializeButtonListeners() {
        const allButtons = document.querySelectorAll('.button-group button');

        allButtons.forEach(button => {
            // Add click event listener with improved handling
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Add visual feedback immediately
                this.style.transform = 'scale(0.95)';

                // Reset transform after a short delay
                setTimeout(() => {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'none';
                    }
                }, 150);
            });

            // Add keyboard support
            button.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });

            // Add focus/blur effects
            button.addEventListener('focus', function() {
                this.style.boxShadow = '0 0 0 3px rgba(8, 107, 183, 0.3)';
            });

            button.addEventListener('blur', function() {
                if (!this.classList.contains('active')) {
                    this.style.boxShadow = 'none';
                }
            });
        });
    }

    // Initialize button listeners
    initializeButtonListeners();

    // Set default active states for buttons
    function setDefaultActiveStates() {
        // Set "Day" as default active for all chart types
        const chartTypes = ['power', 'water', 'flushes'];

        chartTypes.forEach(chartType => {
            updateButtonStates('day', chartType);
        });
    }

    // Set default states
    setDefaultActiveStates();

    // Simulate real-time updates every 30 seconds
    setInterval(() => {
        // Add some variation to the latest data points
        const latestPowerIndex = sampleData.daily.powerConsumption.length - 1;
        const latestWaterIndex = sampleData.daily.waterConsumption.length - 1;
        const latestFlushIndex = sampleData.daily.flushCounts.length - 1;

        // Small random variations
        sampleData.daily.powerConsumption[latestPowerIndex] = (parseFloat(sampleData.daily.powerConsumption[latestPowerIndex]) + (Math.random() - 0.5) * 0.5).toFixed(1);
        sampleData.daily.waterConsumption[latestWaterIndex] = Math.max(0, parseInt(sampleData.daily.waterConsumption[latestWaterIndex]) + Math.floor((Math.random() - 0.5) * 10));
        sampleData.daily.flushCounts[latestFlushIndex] = Math.max(0, parseInt(sampleData.daily.flushCounts[latestFlushIndex]) + Math.floor((Math.random() - 0.5) * 3));

        // Update charts if they're showing daily data
        if (currentTimeframe === 'daily') {
            powerConsumptionChart.data.datasets[0].data = sampleData.daily.powerConsumption;
            waterConsumptionChart.data.datasets[0].data = sampleData.daily.waterConsumption;
            flushesChart.data.datasets[0].data = sampleData.daily.flushCounts;

            powerConsumptionChart.update('none');
            waterConsumptionChart.update('none');
            flushesChart.update('none');

            // Update savings
            updateSavingsWithSampleData();
        }

        // Update septic tank with random variation
        const currentFill = septicTankChart.data.datasets[0].data[1];
        const newFill = Math.max(20, Math.min(80, currentFill + (Math.random() - 0.5) * 5));
        septicTankChart.data.datasets[0].data = [100 - newFill, newFill];
        septicTankChart.update('none');

    }, 30000); // Update every 30 seconds

    // Single section for all Firebase listeners
    try {
        // Log entire database structure
        const rootRef = window.ref(window.database, '/');
        window.onValue(rootRef, (snapshot) => {
            const data = snapshot.val();
            console.log('Entire Database Structure:', data);
            
            // Update individual charts based on the data
            if (data) {
                // Update Flushes Chart
                if (data.FlushCount && data.FlushCount.dailyFlushes) {
                    console.log('FlushCount Data:', data.FlushCount);
                    flushesChart.data.datasets[0].data = data.FlushCount.dailyFlushes;
                    flushesChart.update();
                }

                // Update Power Consumption Chart
                if (data.PowerConsumption && data.PowerConsumption.dailyUsage) {
                    console.log('PowerConsumption Data:', data.PowerConsumption);
                    powerConsumptionChart.data.datasets[0].data = data.PowerConsumption.dailyUsage;
                    powerConsumptionChart.update();
                }

                // Update Water Consumption Chart
if (data.WaterConsumption && data.WaterConsumption.dailyConsumption) {
console.log('Water Consumption Data Found:', {
    rawData: data.WaterConsumption,
    dailyValues: data.WaterConsumption.dailyConsumption,
    unit: data.WaterConsumption.unit
});
waterConsumptionChart.data.datasets[0].data = data.WaterConsumption.dailyConsumption;
waterConsumptionChart.update();
} else {
console.warn('Water Consumption Data Missing or Invalid:', {
    hasWaterConsumption: !!data.WaterConsumption,
    fullData: data.WaterConsumption
});
}

                // Update Septic Tank Chart
                if (data.SepticTankStatus && data.SepticTankStatus.history) {
console.log('SepticTankStatus Data:', data.SepticTankStatus);

// Get the latest value from the history array
const history = data.SepticTankStatus.history;
const latestReading = history[history.length - 1];

if (latestReading && latestReading.value !== undefined) {
    const filledPercentage = latestReading.value;
    console.log('Latest Septic Tank Value:', filledPercentage);
    
    septicTankChart.data.datasets[0].data = [
        100 - filledPercentage,  // Empty portion
        filledPercentage         // Filled portion
    ];
    septicTankChart.update();
} else {
    console.warn('No valid septic tank readings found');
}
} else {
console.warn('Septic Tank Status data missing or invalid');
}
            }
        }, (error) => {
            console.error('Error fetching database:', error);
        });
    } catch (error) {
        console.error('Error setting up Firebase listeners:', error);
    }
});


const header = document.querySelector('.header');
window.addEventListener('scroll', () => {
    // Check if the user has scrolled down
    if (window.scrollY > 50) {
        // Slide the header up
        header.style.top = '-70px';
    } else {
        // Slide the header back down
        header.style.top = '0';
    }
});

const toggle = document.querySelector('.toggle');
window.addEventListener('scroll', () => {
    // Check if the user has scrolled down
    if (window.scrollY > 50) {
        // Slide the toggle up
        toggle.style.top = '70px';

    } else {
        // Slide the toggle back down
        toggle.style.top = '15px';
    }
});


  document.querySelectorAll('footer h2, footer p').forEach(function(element) {
    element.addEventListener('click', function(event) {
      event.preventDefault();
    });
  });


  // Function to calculate water savings
function calculateWaterSavings() {
    const waterUsagePerFlushSterilease = 2; // liters
    const waterUsagePerFlushNormalRestroom = 8; // liters
    const totalFlushes = data.FlushCount.dailyFlushes.reduce((a, b) => a + b, 0);

    const waterSavings = (waterUsagePerFlushNormalRestroom - waterUsagePerFlushSterilease) * totalFlushes;
    const waterSavingsPercentage = ((waterUsagePerFlushNormalRestroom - waterUsagePerFlushSterilease) / waterUsagePerFlushNormalRestroom) * 100;

    return { amount: waterSavings, percentage: waterSavingsPercentage };
}

// Function to calculate electricity savings
function calculateElectricitySavings() {
    const electricityUsagePerFlushSterilease = 0.2; // kWh
    const electricityUsagePerFlushNormalRestroom = 0.5; // kWh
    const totalFlushes = data.FlushCount.dailyFlushes.reduce((a, b) => a + b, 0);

    const electricitySavings = (electricityUsagePerFlushNormalRestroom - electricityUsagePerFlushSterilease) * totalFlushes;
    const electricitySavingsPercentage = ((electricityUsagePerFlushNormalRestroom - electricityUsagePerFlushSterilease) / electricityUsagePerFlushNormalRestroom) * 100;

    return { amount: electricitySavings, percentage: electricitySavingsPercentage };
}

// Function to update the savings section
function updateSavingsSection() {
    const waterSavings = calculateWaterSavings();
    const electricitySavings = calculateElectricitySavings();

    document.getElementById('water-savings-amount').innerText = `${waterSavings.amount} Liters`;
    document.getElementById('water-savings-percentage').innerText = `${waterSavings.percentage}%`;
    document.getElementById('electricity-savings-amount').innerText = `${electricitySavings.amount} kWh`;
    document.getElementById('electricity-savings-percentage').innerText = `${electricitySavings.percentage}%`;
}

// Call the updateSavingsSection function whenever the data changes
onValue(ref(database, 'FlushCount'), (snapshot) => {
    const data = snapshot.val();
    if (data) {
        console.log('Firebase FlushCount data received:', data);
        // You can integrate real Firebase data here when available
        // For now, we're using sample data
    }
    updateSavingsSection();
});