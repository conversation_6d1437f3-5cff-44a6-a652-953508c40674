<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SteriLease Maintenance</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="main.css">
    
</head>
<body>
    <div class="header">
        <button class="logout-button" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
        </button>
    </div>
    <label>
        <input type="checkbox">
        <div class="toggle">
            <span class="top_line common"></span>
            <span class="middle_line common"></span>
            <span class="bottom_line common"></span>
        </div>
        <div class="slide">
            <h3>MENU</h3>
            <ul>
                <li><a href="main.html"><i class="fas fa-tv"></i>Dashboard</li>
                <li><a href="javascript:void(0);" onclick="openPopup()"><i class="far fa-user"></i>About</li>
                <li><a href="#"><i class="fab fa-gripfire"></i>Maintain</li>
                <li onclick="openModal()"><a href="#"><i class="far fa-comments"></i>analyze</li>
                <li onclick="logout()"><a href="#">
                <i class="fas fa-sign-out-alt"></i> logout
                </li>
            </ul>
        </div>
    </label>
    <div class="popup" id="aboutPopup">
        <div class="popup-content">
            <span class="close" onclick="closePopup()">&times;</span>
            <h2 class="popup-title">About SterilEase</span></h2>
            <p class="popup-description">
                SterilEase is a state-of-the-art smart restroom system designed to revolutionize hygiene standards in public spaces. With touchless technology and cutting-edge monitoring tools, it ensures cleanliness, efficiency, and sustainability.
            </p>
            <h3 class="features-title">Key Features</h3>
            <ul class="features-list">
                <li style="color: black;"><i class="fas fa-sun"></i> UV Sterilization</li>
                <li style="color: black;"><i class="fas fa-fan"></i> Vacuum Pump Monitoring</li>
                <li style="color: black;"><i class="fas fa-toilet"></i> Septic Tank Management</li>
                <li style="color: black;"><i class="fas fa-tint"></i> Water and Power Consumption Tracking</li>
            </ul>
        </div>
    </div>
    <!-- Modal for Analyze -->
    <div id="analyzeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h1 class="modal-title">Analyze and Visualize Your Data</h1>
            <p class="modal-intro">
                Welcome to the Analyze section of <strong>SterilEase</strong>. This page is designed to help you visualize and monitor data related to water usage and power consumption over time.
            </p>
    
            <div class="modal-body">
                <img src="/assests/graph1.jpg" alt="Visualization Example" class="modal-image">
                <div class="modal-description">
                    <p>
                        By leveraging interactive graphs, you can track patterns and trends, ensuring efficient management of these vital resources.
                    </p>
                    <p>
                        Use the provided charts to gain insights into resource usage and make informed decisions to optimize operations, reduce waste, and promote sustainability.
                    </p>
                    <p>
                        These tools empower you to take control of your resource management with clarity and precision.
                    </p>
                </div>
            </div>
    
            <h2 class="tools-title">Tools and Features</h2>
            <div class="tools-container">
                <div class="tool">
                    <img src="/assests/charts.jpg" alt="Graph Icon">
                    <p>Interactive Graphs</p>
                </div>
                <div class="tool">
                    <img src="/assests/data insight.jpg" alt="Insights Icon">
                    <p>Data Insights</p>
                </div>
                <div class="tool">
                    <img src="/assests/resource.webp" alt="Management Icon">
                    <p>Resource Management</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h1>SteriLease Maintenance</h1>
        
        <h2 class="section-title">BLDC Motor Issues</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="motorTable">
                <!-- Data will be dynamically inserted here -->
            </tbody>
        </table>

        <h2 class="section-title">Valve Issues</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="valveTable">
                <!-- Data will be dynamically inserted here -->
            </tbody>
        </table>

        <h2 class="section-title">Sensor Issues</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="sensorTable">
                <!-- Data will be dynamically inserted here -->
            </tbody>
        </table>
    </div>
    <footer>
        <div class="footer-container">
            <div class="footer-top">
                <div class="footer-logo">
                    <h2>SterilEase</h2>
                    <p>Copyright &copy; 2025 SterilEase. All rights reserved.</p>
                </div>
                <div class="footer-social-links">
                    <h3>Stay Connected</h3>
                    <ul>
                        <li>
                            <a href="https://www.instagram.com/vikash_s_nair/" target="_blank">
                                <i class="fa fa-instagram" aria-hidden="true"></i>
                                Instagram
                            </a>
                        </li>
                        <li>
                            <a href="mailto:<EMAIL>">
                                <i class="fa fa-envelope" aria-hidden="true"></i>
                                Email
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Designed and Developed by SterilEase</p>
            </div>
        </div>
    </footer>
    <!-- Firebase SDK v11 -->
    <script type="module">
        
        // Import the necessary Firebase functions
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.0.2/firebase-app.js";
        import { getDatabase, ref, onChildAdded } from "https://www.gstatic.com/firebasejs/11.0.2/firebase-database.js";

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyAnU69sqRHEC9xDj7sQkyBH_sOG3AOhF24",
            authDomain: "sterilease-b1185.firebaseapp.com",
            databaseURL: "https://sterilease-b1185-default-rtdb.asia-southeast1.firebasedatabase.app",
            projectId: "sterilease-b1185",
            storageBucket: "sterilease-b1185.firebasestorage.app",
            messagingSenderId: "287619655835",
            appId: "1:287619655835:web:5f188535e492ebc3f33e3e"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);

        // Get a reference to the database
        const db = getDatabase(app);

        // Get references to the table bodies where data will be inserted
        const motorTable = document.getElementById("motorTable");
        const valveTable = document.getElementById("valveTable");
        const sensorTable = document.getElementById("sensorTable");

        // Reference to the maintenance data in the database
        const maintenanceRef = ref(db, 'maintenanceData');

        // Sample Data (5 entries)
        const sampleData = [
            { timestamp: 1678905600, motor_status: '1', valve_status: '0', sensor_status: '1' },
            { timestamp: 1678909200, motor_status: '0', valve_status: '1', sensor_status: '0' },
            { timestamp: 1678912800, motor_status: '1', valve_status: '1', sensor_status: '1' },
            { timestamp: 1678916400, motor_status: '0', valve_status: '0', sensor_status: '1' },
            { timestamp: 1678920000, motor_status: '1', valve_status: '0', sensor_status: '0' }
        ];

        // Loop through the sample data to simulate real-time database updates
        sampleData.forEach(data => {
            const timestamp = new Date(data.timestamp * 1000);  // Convert to date
            const date = timestamp.toLocaleDateString();
            const time = timestamp.toLocaleTimeString();

            // Motor, Valve, and Sensor status
            const motorStatus = data.motor_status;
            const valveStatus = data.valve_status;
            const sensorStatus = data.sensor_status;

            // Add rows dynamically to motor table
            motorTable.innerHTML += `
                <tr>
                    <td>${date}</td>
                    <td>${time}</td>
                    <td class="${motorStatus === '1' ? 'issue' : 'resolved'}">
                        ${motorStatus === '1' ? 'Problem Detected' : 'Resolved'}
                    </td>
                </tr>
            `;

            // Add rows dynamically to valve table
            valveTable.innerHTML += `
                <tr>
                    <td>${date}</td>
                    <td>${time}</td>
                    <td class="${valveStatus === '1' ? 'issue' : 'resolved'}">
                        ${valveStatus === '1' ? 'Problem Detected' : 'Resolved'}
                    </td>
                </tr>
            `;

            // Add rows dynamically to sensor table
            sensorTable.innerHTML += `
                <tr>
                    <td>${date}</td>
                    <td>${time}</td>
                    <td class="${sensorStatus === '1' ? 'issue' : 'resolved'}">
                        ${sensorStatus === '1' ? 'Problem Detected' : 'Resolved'}
                    </td>
                </tr>
            `;
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <!-- Including Chart.js -->
    <script src="main.js"></script>
    <style>
        .container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #052f5f;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            margin-bottom: 40px;
        }

        table th, table td {
            padding: 12px;
            text-align: left;
            border: 1px solid #ccc;
        }

        table th {
            background-color: #052f5f;
            color: #ffffff;
        }

        table tr:nth-child(even) {
            background-color: #f1f1f1;
        }

        .section-title {
            color: #052f5f;
            font-size: 1.5em;
            margin-top: 20px;
        }

        .issue {
            color: #d32f2f;
            font-weight: bold;
        }

        .resolved {
            color: #052f5f;
            font-weight: bold;
        }
    </style>
    <button class="home-button" onclick="location.href='main.html'">
        <i class="fas fa-home"></i>
    </button>
</body>
</html>