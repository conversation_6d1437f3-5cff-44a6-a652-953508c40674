// Firebase Config
const firebaseConfig = {
    apiKey: "YOUR_API_KEY",
    authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
    databaseURL: "https://YOUR_PROJECT_ID.firebaseio.com",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_PROJECT_ID.appspot.com",
    messagingSenderId: "YOUR_SENDER_ID",
    appId: "YOUR_APP_ID",
  };
  
  // Initialize Firebase
  const app = firebase.initializeApp(firebaseConfig);
  const database = firebase.database();
  
  // Fetch Data Function
  document.getElementById("fetch-data").addEventListener("click", async () => {
    const uid = prompt("Enter your UID:");
    if (uid) {
      const dataRef = firebase.database().ref(`users/${uid}`);
      dataRef.once("value", (snapshot) => {
        const data = snapshot.val();
        document.getElementById("output").textContent = JSON.stringify(data, null, 2);
      });
    }
  });
  